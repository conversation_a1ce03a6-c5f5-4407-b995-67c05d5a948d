#!/bin/bash

# Test script to demonstrate the fix for renewal service status persistence
# This script tests the renewal service without starting btfs daemon

echo "=== Testing Renewal Service Status Persistence ==="
echo

# Build the btfs binary
echo "Building btfs binary..."
go build -o ./btfs-test ./cmd/btfs/
if [ $? -ne 0 ]; then
    echo "Failed to build btfs binary"
    exit 1
fi

echo "Built btfs binary successfully"
echo

# Initialize a test repo if it doesn't exist
if [ ! -d ".btfs-test" ]; then
    echo "Initializing test BTFS repo..."
    ./btfs-test init --profile=test
    if [ $? -ne 0 ]; then
        echo "Failed to initialize BTFS repo"
        exit 1
    fi
    echo "Initialized test repo"
    echo
fi

# Set the repo path
export BTFS_PATH=".btfs-test"

echo "=== Test 1: Check initial status (should be not running) ==="
./btfs-test storage upload renew service status
echo

echo "=== Test 2: Start the renewal service ==="
./btfs-test storage upload renew service start
echo

echo "=== Test 3: Check status after start (should show running with persistent state) ==="
./btfs-test storage upload renew service status
echo

echo "=== Test 4: Check status again in a new process (this should now work!) ==="
./btfs-test storage upload renew service status
echo

echo "=== Test 5: Stop the service ==="
./btfs-test storage upload renew service stop
echo

echo "=== Test 6: Check status after stop (should be not running) ==="
./btfs-test storage upload renew service status
echo

echo "=== Test completed ==="
echo "The fix allows the service status to persist across different btfs command invocations"
echo "without requiring a running btfs daemon."

# Cleanup
rm -f ./btfs-test
