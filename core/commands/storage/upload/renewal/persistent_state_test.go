package renewal

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/bittorrent/go-btfs/core"
	"github.com/bittorrent/go-btfs/repo"
	uh "github.com/bittorrent/go-btfs/core/commands/storage/upload/helper"
	"github.com/ipfs/go-datastore"
	dssync "github.com/ipfs/go-datastore/sync"
	"github.com/libp2p/go-libp2p/core/peer"
)

// MockContextParams creates a mock context for testing
func createMockContextParams() *uh.ContextParams {
	ds := dssync.MutexWrap(datastore.NewMapDatastore())

	// Create a mock node with datastore
	mockNode := &core.IpfsNode{}

	// Set a test peer ID
	testPeerID, _ := peer.Decode("12D3KooWGzxzKZYveHXtpG6AsrUJBcWxHBFS2HsEoGTxrMLvKXtf")
	mockNode.Identity = testPeerID

	// Use mock repo
	mockRepo := &repo.Mock{
		D: ds,
	}
	mockNode.Repo = mockRepo

	return &uh.ContextParams{
		Ctx: context.Background(),
		N:   mockNode,
	}
}

func TestPersistentServiceState(t *testing.T) {
	ctxParams := createMockContextParams()
	
	// Test saving and loading service state
	originalState := &PersistentServiceState{
		Running:   true,
		PID:       12345,
		StartTime: time.Now(),
		NodeID:    ctxParams.N.Identity.String(),
	}
	
	// Save state
	err := SaveServiceState(ctxParams, originalState)
	if err != nil {
		t.Fatalf("Failed to save service state: %v", err)
	}
	
	// Load state
	loadedState, err := LoadServiceState(ctxParams)
	if err != nil {
		t.Fatalf("Failed to load service state: %v", err)
	}
	
	// Verify state
	if loadedState.Running != originalState.Running {
		t.Errorf("Expected Running=%v, got %v", originalState.Running, loadedState.Running)
	}
	if loadedState.PID != originalState.PID {
		t.Errorf("Expected PID=%d, got %d", originalState.PID, loadedState.PID)
	}
	if loadedState.NodeID != originalState.NodeID {
		t.Errorf("Expected NodeID=%s, got %s", originalState.NodeID, loadedState.NodeID)
	}
}

func TestLoadNonExistentState(t *testing.T) {
	ctxParams := createMockContextParams()
	
	// Load state when none exists
	state, err := LoadServiceState(ctxParams)
	if err != nil {
		t.Fatalf("Failed to load non-existent state: %v", err)
	}
	
	// Should return default state
	if state.Running != false {
		t.Errorf("Expected Running=false for non-existent state, got %v", state.Running)
	}
}

func TestClearServiceState(t *testing.T) {
	ctxParams := createMockContextParams()
	
	// Save a state first
	state := &PersistentServiceState{
		Running: true,
		PID:     12345,
		NodeID:  ctxParams.N.Identity.String(),
	}
	
	err := SaveServiceState(ctxParams, state)
	if err != nil {
		t.Fatalf("Failed to save service state: %v", err)
	}
	
	// Clear the state
	err = ClearServiceState(ctxParams)
	if err != nil {
		t.Fatalf("Failed to clear service state: %v", err)
	}
	
	// Verify state is cleared
	loadedState, err := LoadServiceState(ctxParams)
	if err != nil {
		t.Fatalf("Failed to load state after clearing: %v", err)
	}
	
	if loadedState.Running != false {
		t.Errorf("Expected Running=false after clearing, got %v", loadedState.Running)
	}
}

func TestIsProcessRunning(t *testing.T) {
	// Test with current process (should be running)
	currentPID := os.Getpid()
	if !IsProcessRunning(currentPID) {
		t.Logf("Current process PID %d should be running, but IsProcessRunning returned false", currentPID)
		// This might fail on some systems due to permission issues, so we'll just log it
	}
	
	// Test with invalid PID
	if IsProcessRunning(-1) {
		t.Errorf("Invalid PID should not be running")
	}
	
	if IsProcessRunning(0) {
		t.Errorf("PID 0 should not be running")
	}
	
	// Test with very high PID (likely not running)
	if IsProcessRunning(999999) {
		t.Logf("Warning: PID 999999 appears to be running, this might be a false positive")
	}
}

func TestServiceManagerWithPersistentState(t *testing.T) {
	ctxParams := createMockContextParams()
	manager := NewRenewalServiceManager()
	
	// Initially should not be running
	if manager.IsRunning(ctxParams) {
		t.Errorf("Service should not be running initially")
	}
	
	// Test status when not running
	status := GetRenewalServiceStatus(ctxParams)
	if status.Running {
		t.Errorf("Status should show not running initially")
	}
	
	// Note: We can't easily test the actual start/stop functionality in unit tests
	// because it involves creating background processes and goroutines
	// This would require integration tests
}
